import React, { useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { gsap } from 'gsap'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useDashboard } from '@/hooks/useDashboard'
import {
  FolderOpen,
  MessageSquare,
  FileText,
  CreditCard,
  TrendingUp,
  Clock,
  CheckCircle,
  ArrowRight,
  Loader2
} from 'lucide-react'

const DashboardOverview: React.FC = () => {
  const { data: dashboardData, isLoading, error } = useDashboard()

  useEffect(() => {
    if (!isLoading && dashboardData) {
      // Animate cards on load
      gsap.fromTo(
        '.dashboard-card',
        { y: 30, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power2.out'
        }
      )
    }
  }, [isLoading, dashboardData])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Failed to load dashboard data</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  if (!dashboardData) {
    return null
  }

  const { stats, projects, messages, payments } = dashboardData

  // Stats cards data
  const statsCards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      description: `${stats.activeProjects} active`,
      icon: FolderOpen,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      link: '/dashboard/projects'
    },
    {
      title: 'Unread Messages',
      value: stats.pendingMessages,
      description: 'Requires attention',
      icon: MessageSquare,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      link: '/dashboard/messages'
    },
    {
      title: 'Documents',
      value: stats.recentDocuments,
      description: 'Available for download',
      icon: FileText,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      link: '/dashboard/documents'
    },
    {
      title: 'Total Spent',
      value: `₦${stats.totalSpent.toLocaleString()}`,
      description: 'All time',
      icon: CreditCard,
      color: 'text-agency-green',
      bgColor: 'bg-agency-green/10',
      link: '/dashboard/payments'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="dashboard-card">
        <Card className="bg-gradient-to-r from-agency-green/10 to-agency-green/5 border-agency-green/20">
          <CardHeader>
            <CardTitle className="text-white text-2xl">
              Welcome to Your Dashboard
            </CardTitle>
            <CardDescription className="text-agency-white-muted">
              Track your projects, communicate with our team, and manage your
              account all in one place.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <Card
            key={stat.title}
            className="dashboard-card bg-agency-darker/50 border-agency-green/10 hover:border-agency-green/20 transition-colors"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-agency-white-muted">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white mb-1">
                {stat.value}
              </div>
              <p className="text-xs text-agency-white-muted mb-3">
                {stat.description}
              </p>
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="w-full justify-between text-agency-green hover:bg-agency-green/10"
              >
                <Link to={stat.link}>
                  View Details
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Projects */}
        <Card className="dashboard-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <FolderOpen className="h-5 w-5 text-agency-green" />
              Recent Projects
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {projects.slice(0, 3).map((project) => (
              <div
                key={project.id}
                className="flex items-center justify-between p-3 bg-agency-dark/50 rounded-lg"
              >
                <div className="flex-1">
                  <h4 className="text-white font-medium">{project.name}</h4>
                  <p className="text-sm text-agency-white-muted">
                    {project.package_type}
                  </p>
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs text-agency-white-muted mb-1">
                      <span>Progress</span>
                      <span>{project.progress_percentage}%</span>
                    </div>
                    <Progress
                      value={project.progress_percentage}
                      className="h-2"
                    />
                  </div>
                </div>
                <Badge
                  className={`ml-4 ${
                    project.status === 'completed'
                      ? 'bg-green-500/10 text-green-500'
                      : project.status === 'in_progress'
                      ? 'bg-blue-500/10 text-blue-500'
                      : 'bg-yellow-500/10 text-yellow-500'
                  }`}
                >
                  {project.status.replace('_', ' ')}
                </Badge>
              </div>
            ))}
            {projects.length === 0 && (
              <p className="text-agency-white-muted text-center py-4">
                No projects yet. Contact us to get started!
              </p>
            )}
            <Button variant="outline" className="w-full" asChild>
              <Link to="/dashboard/projects">View All Projects</Link>
            </Button>
          </CardContent>
        </Card>

        {/* Recent Messages */}
        <Card className="dashboard-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-agency-green" />
              Recent Messages
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {messages.slice(0, 3).map((thread) => (
              <div
                key={thread.id}
                className="flex items-start gap-3 p-3 bg-agency-dark/50 rounded-lg"
              >
                <div className="flex-1">
                  <h4 className="text-white font-medium">{thread.subject}</h4>
                  <p className="text-sm text-agency-white-muted line-clamp-2">
                    {thread.lastMessage.content}
                  </p>
                  <p className="text-xs text-agency-white-muted mt-1">
                    {new Date(
                      thread.lastMessage.created_at
                    ).toLocaleDateString()}
                  </p>
                </div>
                {thread.unreadCount > 0 && (
                  <Badge className="bg-agency-green text-agency-dark">
                    {thread.unreadCount}
                  </Badge>
                )}
              </div>
            ))}
            {messages.length === 0 && (
              <p className="text-agency-white-muted text-center py-4">
                No messages yet. Start a conversation with our team!
              </p>
            )}
            <Button variant="outline" className="w-full" asChild>
              <Link to="/dashboard/messages">View All Messages</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="dashboard-card bg-agency-darker/50 border-agency-green/10">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
          <CardDescription className="text-agency-white-muted">
            Common tasks you might want to perform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
              asChild
            >
              <Link to="/dashboard/messages">
                <MessageSquare className="mr-2 h-4 w-4" />
                Send Message
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/dashboard/documents">
                <FileText className="mr-2 h-4 w-4" />
                View Documents
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/contact">
                <Clock className="mr-2 h-4 w-4" />
                Schedule Call
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DashboardOverview
