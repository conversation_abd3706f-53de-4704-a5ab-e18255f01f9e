/**
 * Registration form types and interfaces
 * Following TypeScript best practices and SOLID principles
 */

export interface RegistrationFormData {
  fullName: string
  email: string
  phone?: string
  motivation: 'build-apps-ai' | 'fullstack-developer' | 'career-opportunities' | 'launch-startup' | 'other'
  paymentIntent: 'pay-now' | 'reserve-later'
}

export interface RegistrationResponse {
  success: boolean
  message: string
  registrationId?: string
  paymentUrl?: string
  error?: string
}

export interface RegistrationSubmissionState {
  isSubmitting: boolean
  isSubmitted: boolean
  error: string | null
  response: RegistrationResponse | null
}

export interface RegistrationApiPayload extends RegistrationFormData {
  eventTitle: string
  timestamp: string
  source: 'modal' | 'page'
}

// Form validation error types
export interface FormValidationError {
  field: keyof RegistrationFormData
  message: string
}

// Event registration configuration
export interface RegistrationConfig {
  eventTitle: string
  maxParticipants: number
  currentParticipants: number
  registrationDeadline: string
  paymentRequired: boolean
  allowReservation: boolean
  reservationHoldHours: number
}
