/**
 * Registration form action handlers
 * Following SOLID principles and clean architecture
 */

import { RegistrationFormData, RegistrationResponse, RegistrationApiPayload } from '@/types/registration'

/**
 * Submit registration data to the backend API
 * This is a placeholder implementation that can be replaced with actual API calls
 */
export async function submitRegistration(
  formData: RegistrationFormData,
  eventTitle: string
): Promise<RegistrationResponse> {
  try {
    // Prepare the payload
    const payload: RegistrationApiPayload = {
      ...formData,
      eventTitle,
      timestamp: new Date().toISOString(),
      source: 'modal'
    }

    // Log the registration data for development
    console.log('Registration submission:', payload)

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Simulate different response scenarios based on payment intent
    if (formData.paymentIntent === 'pay-now') {
      return {
        success: true,
        message: 'Registration successful! Redirecting to payment...',
        registrationId: `reg_${Date.now()}`,
        paymentUrl: 'https://payment.kavaradigital.online/checkout'
      }
    } else {
      return {
        success: true,
        message: 'Spot reserved successfully! You have 48 hours to complete payment.',
        registrationId: `reg_${Date.now()}`
      }
    }
  } catch (error) {
    console.error('Registration submission failed:', error)
    return {
      success: false,
      message: 'Registration failed. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Validate registration data before submission
 * Additional business logic validation beyond form validation
 */
export function validateRegistrationData(
  formData: RegistrationFormData,
  eventConfig?: { maxParticipants: number; currentParticipants: number }
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check if event is full
  if (eventConfig && eventConfig.currentParticipants >= eventConfig.maxParticipants) {
    errors.push('Sorry, this event is fully booked.')
  }

  // Additional business rules can be added here
  // For example: checking if email is already registered, etc.

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Format registration data for display
 */
export function formatRegistrationData(data: RegistrationFormData): Record<string, string> {
  const motivationLabels = {
    'build-apps-ai': 'I want to build apps with AI',
    'fullstack-developer': 'I want to become a full-stack developer',
    'career-opportunities': "I'm exploring career opportunities",
    'launch-startup': 'I want to launch a startup',
    'other': 'Other'
  }

  const paymentIntentLabels = {
    'pay-now': 'Pay Now',
    'reserve-later': 'Reserve Now, Pay Later (48h Hold)'
  }

  return {
    'Full Name': data.fullName,
    'Email': data.email,
    'Phone': data.phone || 'Not provided',
    'Motivation': motivationLabels[data.motivation],
    'Payment Intent': paymentIntentLabels[data.paymentIntent]
  }
}

/**
 * Handle post-registration actions
 * This could include analytics tracking, email notifications, etc.
 */
export async function handlePostRegistration(
  response: RegistrationResponse,
  formData: RegistrationFormData
): Promise<void> {
  if (response.success) {
    // Track successful registration
    console.log('Registration successful:', {
      registrationId: response.registrationId,
      email: formData.email,
      paymentIntent: formData.paymentIntent
    })

    // Here you could add:
    // - Analytics tracking (Google Analytics, Mixpanel, etc.)
    // - Email notifications
    // - CRM integration
    // - Slack notifications for the team
  }
}
