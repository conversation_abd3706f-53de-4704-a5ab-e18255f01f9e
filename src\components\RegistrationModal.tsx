import React, { useEffect, useRef, useState } from 'react'
import { X, Lock, CheckCircle, Loader2 } from 'lucide-react'
import { gsap } from 'gsap'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import {
  submitRegistration,
  handlePostRegistration
} from '@/lib/registrationActions'
import type { RegistrationResponse } from '@/types/registration'

// Form validation schema
const registrationSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  motivation: z.enum(
    [
      'build-apps-ai',
      'fullstack-developer',
      'career-opportunities',
      'launch-startup',
      'other'
    ],
    {
      required_error: 'Please select your motivation for joining'
    }
  ),
  paymentIntent: z.enum(['pay-now', 'reserve-later'], {
    required_error: 'Please select your payment preference'
  })
})

type RegistrationFormData = z.infer<typeof registrationSchema>

interface RegistrationModalProps {
  isOpen: boolean
  onClose: () => void
  eventTitle?: string
  registrationUrl?: string
}

const RegistrationModal: React.FC<RegistrationModalProps> = ({
  isOpen,
  onClose,
  eventTitle = 'AI & Code Workshop',
  registrationUrl = 'https://forms.gle/T7gPtiTs1RJiK5E76'
}) => {
  const modalRef = useRef<HTMLDivElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [registrationResponse, setRegistrationResponse] =
    useState<RegistrationResponse | null>(null)

  // Form setup with React Hook Form and Zod validation
  const form = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      motivation: undefined,
      paymentIntent: undefined
    }
  })

  // Form submission handler
  const onSubmit = async (data: RegistrationFormData) => {
    setIsSubmitting(true)
    try {
      // Ensure required fields are present (form validation should handle this)
      const formData = {
        fullName: data.fullName!,
        email: data.email!,
        phone: data.phone || '',
        motivation: data.motivation!,
        paymentIntent: data.paymentIntent!
      }

      // Submit registration using the action handler
      const response = await submitRegistration(formData, eventTitle)
      setRegistrationResponse(response)

      if (response.success) {
        setIsSubmitted(true)
        // Handle post-registration actions
        await handlePostRegistration(response, formData)

        // Reset form after successful submission
        form.reset()

        // If payment is required and URL is provided, redirect after delay
        if (response.paymentUrl && formData.paymentIntent === 'pay-now') {
          setTimeout(() => {
            window.open(response.paymentUrl, '_blank')
            setIsSubmitted(false)
            onClose()
          }, 2000)
        } else {
          // Close modal after showing success message
          setTimeout(() => {
            setIsSubmitted(false)
            onClose()
          }, 3000)
        }
      } else {
        // Handle registration failure
        console.error('Registration failed:', response.error)
        // You could show a toast notification here
      }
    } catch (error) {
      console.error('Registration submission error:', error)
      setRegistrationResponse({
        success: false,
        message: 'An unexpected error occurred. Please try again.',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'

      // Animate modal in
      if (overlayRef.current && contentRef.current) {
        gsap.set(overlayRef.current, { opacity: 0 })
        gsap.set(contentRef.current, { scale: 0.8, opacity: 0 })

        gsap.to(overlayRef.current, { opacity: 1, duration: 0.3 })
        gsap.to(contentRef.current, {
          scale: 1,
          opacity: 1,
          duration: 0.4,
          ease: 'back.out(1.7)',
          delay: 0.1
        })
      }
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleClose = () => {
    if (overlayRef.current && contentRef.current) {
      gsap.to(contentRef.current, {
        scale: 0.8,
        opacity: 0,
        duration: 0.2
      })
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        onComplete: onClose
      })
    } else {
      onClose()
    }
  }

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  const handleEscapeKey = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey)
      return () => document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-agency-dark text-white">
          <div>
            <h2 className="text-2xl font-bold">Register for {eventTitle}</h2>
            <p className="text-agency-white-muted mt-1">
              Secure your spot - Limited seats available!
            </p>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-agency-green/10 rounded-full transition-colors"
            aria-label="Close modal"
          >
            <X size={24} className="text-agency-white-muted hover:text-white" />
          </button>
        </div>

        {/* Form Container */}
        <div className="relative h-[600px] md:h-[700px] overflow-y-auto">
          {isSubmitted ? (
            // Success State
            <div className="flex items-center justify-center h-full bg-green-50">
              <div className="text-center p-8">
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-green-700 mb-2">
                  Registration Successful!
                </h3>
                <p className="text-green-600 mb-4">
                  {registrationResponse?.message ||
                    `Your spot has been reserved for ${eventTitle}`}
                </p>
                {registrationResponse?.paymentUrl && (
                  <p className="text-sm text-blue-600 mb-2">
                    Redirecting to payment page...
                  </p>
                )}
                <p className="text-sm text-gray-600">
                  You'll receive a confirmation email shortly with next steps.
                </p>
                {registrationResponse?.registrationId && (
                  <p className="text-xs text-gray-500 mt-2">
                    Registration ID: {registrationResponse.registrationId}
                  </p>
                )}
              </div>
            </div>
          ) : registrationResponse && !registrationResponse.success ? (
            // Error State
            <div className="flex items-center justify-center h-full bg-red-50">
              <div className="text-center p-8">
                <X className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-red-700 mb-2">
                  Registration Failed
                </h3>
                <p className="text-red-600 mb-4">
                  {registrationResponse.message}
                </p>
                <Button
                  onClick={() => {
                    setRegistrationResponse(null)
                    form.reset()
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            // Registration Form
            <div className="p-6">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  {/* Section 1: Essential Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                      Essential Information
                    </h3>

                    <FormField
                      control={form.control}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter your full name"
                              {...field}
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address *</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Enter your email address"
                              {...field}
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="Enter your phone number"
                              {...field}
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Section 2: Engagement Qualification */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                      Engagement Qualification
                    </h3>

                    <FormField
                      control={form.control}
                      name="motivation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Why do you want to join the workshop? *
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select your motivation" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="build-apps-ai">
                                I want to build apps with AI
                              </SelectItem>
                              <SelectItem value="fullstack-developer">
                                I want to become a full-stack developer
                              </SelectItem>
                              <SelectItem value="career-opportunities">
                                I'm exploring career opportunities
                              </SelectItem>
                              <SelectItem value="launch-startup">
                                I want to launch a startup
                              </SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Section 3: Payment Intent */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                      Payment Intent
                    </h3>

                    <FormField
                      control={form.control}
                      name="paymentIntent"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>
                            Do you want to pay now or reserve your spot? *
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col space-y-2"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="pay-now" id="pay-now" />
                                <Label htmlFor="pay-now">Pay Now</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem
                                  value="reserve-later"
                                  id="reserve-later"
                                />
                                <Label htmlFor="reserve-later">
                                  Reserve Now, Pay Later (48h Hold)
                                </Label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Section 4: Submission */}
                  <div className="space-y-4">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-agency-green hover:bg-agency-green/90 text-white font-bold py-4 px-6 rounded-lg text-lg"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Lock className="mr-2 h-5 w-5" />✅ Reserve My Spot —
                          Limited to 47
                        </>
                      )}
                    </Button>

                    <div className="text-center text-sm text-gray-600">
                      🔒 100% Secure Registration. SSL Encrypted. Money-Back
                      Guarantee.
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>🔒 Secure Registration</span>
              <span>📞 Need help? Call 0802-902-0121</span>
            </div>
            <button
              onClick={handleClose}
              className="text-agency-green hover:text-agency-green/80 font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RegistrationModal
