
import React, { useState } from 'react';
import { Button } from './ui/button';
import { Checkbox } from './ui/checkbox';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Textarea } from './ui/textarea';
import { Input } from './ui/input';
import { Slider } from './ui/slider';

const ContactForm = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    message: '',
    budget: 5000,
    reasons: {
      webDesign: false,
      mobileApp: false,
      collaboration: false,
      other: false
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleCheckboxChange = (name: string) => {
    setFormData({
      ...formData,
      reasons: {
        ...formData.reasons,
        [name]: !formData.reasons[name as keyof typeof formData.reasons]
      }
    });
  };

  const handleRangeChange = (value: number[]) => {
    setFormData({
      ...formData,
      budget: value[0]
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    setIsSubmitted(true);
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  return (
    <div className="max-w-3xl mx-auto">
      <form
        onSubmit={handleSubmit}
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        <div className="space-y-2">
          <label htmlFor="fullName" className="block text-sm">
            Full Name
          </label>
          <Input
            type="text"
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="Type here"
            className="w-full bg-agency-darker border border-agency-white-muted/20 rounded-md"
            required
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm">
            Email
          </label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Type here"
            className="w-full bg-agency-darker border border-agency-white-muted/20 rounded-md"
            required
          />
        </div>

        <div className="md:col-span-2 space-y-3 text-white">
          <p className="block text-sm mb-2">Why are you contacting us?</p>
          <div className="grid grid-cols-2 gap-3">
            <label className="flex items-center space-x-3">
              <Checkbox
                className="border-agency-green"
                checked={formData.reasons.webDesign}
                onCheckedChange={() => handleCheckboxChange('webDesign')}
              />
              <span>Web Design</span>
            </label>
            <label className="flex items-center space-x-3">
              <Checkbox
                className="border-agency-green"
                checked={formData.reasons.mobileApp}
                onCheckedChange={() => handleCheckboxChange('mobileApp')}
              />
              <span>Mobile App Design</span>
            </label>
            <label className="flex items-center space-x-3">
              <Checkbox
                className="border-agency-green"
                checked={formData.reasons.collaboration}
                onCheckedChange={() => handleCheckboxChange('collaboration')}
              />
              <span>Collaboration</span>
            </label>
            <label className="flex items-center space-x-3">
              <Checkbox
                className="border-agency-green"
                checked={formData.reasons.other}
                onCheckedChange={() => handleCheckboxChange('other')}
              />
              <span>Others</span>
            </label>
          </div>
        </div>

        <div className="md:col-span-2 space-y-2">
          <label htmlFor="budget" className="block text-sm mb-2">
            Your Budget
          </label>
          <div className="mb-2">
            <span className="text-sm text-agency-white-muted">
              Slide to indicate your budget range
            </span>
          </div>
          <div className="px-1 py-4">
            <Slider
              defaultValue={[5000]}
              max={50000}
              min={1000}
              step={1000}
              value={[formData.budget]}
              onValueChange={handleRangeChange}
              className="w-full bg-agency-green"
            />
            <div className="flex justify-between mt-2 text-sm text-agency-white-muted">
              <span>$1000</span>
              <span>${formData.budget}</span>
              <span>$50000</span>
            </div>
          </div>
        </div>

        <div className="md:col-span-2 space-y-2">
          <label htmlFor="message" className="block text-sm">
            Your Message
          </label>
          <Textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Type here"
            rows={4}
            className="w-full bg-agency-darker border border-agency-white-muted/20 rounded-md resize-none"
            required
          />
        </div>

        <div className="md:col-span-2 flex justify-center mt-4">
          <Button
            type="submit"
            className="bg-agency-green text-agency-dark font-medium px-10 py-6 rounded-md hover:bg-opacity-90 transition-all"
          >
            Submit
          </Button>
        </div>
      </form>

      <Dialog open={isSubmitted} onOpenChange={setIsSubmitted}>
        <DialogContent className="bg-agency-darker text-white border-agency-white-muted/20">
          <DialogHeader>
            <DialogTitle>Form Submitted</DialogTitle>
          </DialogHeader>
          <p>Thank you for your message! We'll be in touch soon.</p>
        </DialogContent>
      </Dialog>
    </div>
  )
};

export default ContactForm;
