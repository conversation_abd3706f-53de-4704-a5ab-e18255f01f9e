import React, { useState, useEffect } from 'react'
import { gsap } from 'gsap'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { useMessages, useSendMessage } from '@/hooks/useDashboard'
import { MessageSquare, Send, Plus, Loader2, User, Clock } from 'lucide-react'

const MessagesPage: React.FC = () => {
  const { data: messages, isLoading, error } = useMessages()
  const sendMessageMutation = useSendMessage()
  const [selectedThread, setSelectedThread] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState({ subject: '', content: '' })
  const [showNewMessage, setShowNewMessage] = useState(false)

  useEffect(() => {
    if (!isLoading && messages) {
      gsap.fromTo(
        '.message-item',
        { x: -30, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power2.out'
        }
      )
    }
  }, [isLoading, messages])

  const handleSendMessage = async () => {
    if (!newMessage.subject.trim() || !newMessage.content.trim()) return

    try {
      await sendMessageMutation.mutateAsync({
        subject: newMessage.subject,
        content: newMessage.content
      })
      setNewMessage({ subject: '', content: '' })
      setShowNewMessage(false)
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading your messages...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Failed to load messages</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Messages</h1>
          <p className="text-agency-white-muted mt-1">
            Communicate directly with our team about your projects
          </p>
        </div>
        <Button
          className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
          onClick={() => setShowNewMessage(true)}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Message
        </Button>
      </div>

      {/* New Message Form */}
      {showNewMessage && (
        <Card className="bg-agency-darker/50 border-agency-green/20">
          <CardHeader>
            <CardTitle className="text-white">Send New Message</CardTitle>
            <CardDescription className="text-agency-white-muted">
              Start a new conversation with our team
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="Message subject"
              value={newMessage.subject}
              onChange={(e) =>
                setNewMessage((prev) => ({ ...prev, subject: e.target.value }))
              }
              className="bg-agency-dark border-agency-green/20 text-white"
            />
            <Textarea
              placeholder="Type your message here..."
              value={newMessage.content}
              onChange={(e) =>
                setNewMessage((prev) => ({ ...prev, content: e.target.value }))
              }
              className="bg-agency-dark border-agency-green/20 text-white min-h-[120px]"
            />
            <div className="flex gap-2">
              <Button
                onClick={handleSendMessage}
                disabled={
                  sendMessageMutation.isPending ||
                  !newMessage.subject.trim() ||
                  !newMessage.content.trim()
                }
                className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
              >
                {sendMessageMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send Message
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewMessage(false)
                  setNewMessage({ subject: '', content: '' })
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Messages List */}
      {messages && messages.length > 0 ? (
        <div className="space-y-4">
          {messages.map((thread) => (
            <Card
              key={thread.id}
              className="message-item bg-agency-darker/50 border-agency-green/10 hover:border-agency-green/20 transition-all duration-300 cursor-pointer"
              onClick={() =>
                setSelectedThread(
                  selectedThread === thread.id ? null : thread.id
                )
              }
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-agency-green/10 rounded-full">
                      <MessageSquare className="h-4 w-4 text-agency-green" />
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">
                        {thread.subject}
                      </CardTitle>
                      <CardDescription className="text-agency-white-muted">
                        {thread.messageCount} message
                        {thread.messageCount !== 1 ? 's' : ''}
                        {thread.projectName && ` • ${thread.projectName}`}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {thread.unreadCount > 0 && (
                      <Badge className="bg-agency-green text-agency-dark">
                        {thread.unreadCount} new
                      </Badge>
                    )}
                    <div className="text-xs text-agency-white-muted flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(
                        thread.lastMessage.created_at
                      ).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="flex items-start gap-3">
                  <div className="p-1 bg-agency-green/10 rounded-full">
                    <User className="h-3 w-3 text-agency-green" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-agency-white-muted">
                      <span className="font-medium text-white">
                        {thread.lastMessage.sender_name}:
                      </span>{' '}
                      {thread.lastMessage.content.length > 150
                        ? `${thread.lastMessage.content.substring(0, 150)}...`
                        : thread.lastMessage.content}
                    </p>
                  </div>
                </div>

                {/* Expanded thread view */}
                {selectedThread === thread.id && (
                  <div className="mt-4 pt-4 border-t border-agency-green/10">
                    <div className="bg-agency-dark/50 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-2">
                        Full Conversation
                      </h4>
                      <p className="text-agency-white-muted text-sm mb-4">
                        This is a preview. Full conversation history will be
                        implemented with real message data.
                      </p>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
                        >
                          <Send className="mr-2 h-3 w-3" />
                          Reply
                        </Button>
                        <Button size="sm" variant="outline">
                          Mark as Read
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="bg-agency-darker/50 border-agency-green/10">
          <CardContent className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-agency-white-muted mx-auto mb-4" />
            <h3 className="text-white text-lg font-medium mb-2">
              No Messages Yet
            </h3>
            <p className="text-agency-white-muted mb-6">
              Start a conversation with our team to get help with your projects
              or ask questions.
            </p>
            <Button
              className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
              onClick={() => setShowNewMessage(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Send Your First Message
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default MessagesPage
