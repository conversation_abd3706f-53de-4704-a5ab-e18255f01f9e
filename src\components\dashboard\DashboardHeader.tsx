import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Menu, Bell, User, Settings, LogOut, RefreshCw } from 'lucide-react'
import { User as UserType } from '@/lib/supabase'

interface DashboardHeaderProps {
  user: UserType | null
  onMenuClick: () => void
  showMenuButton?: boolean
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  user,
  onMenuClick,
  showMenuButton = false
}) => {
  const { signOut, refreshPaymentStatus } = useAuth()
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const handleRefreshPayment = async () => {
    setIsRefreshing(true)
    try {
      await refreshPaymentStatus()
    } catch (error) {
      console.error('Error refreshing payment status:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <Badge className="bg-green-500/10 text-green-500 border-green-500/20">
            Active
          </Badge>
        )
      case 'pending':
        return (
          <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
            Pending
          </Badge>
        )
      case 'expired':
        return (
          <Badge className="bg-red-500/10 text-red-500 border-red-500/20">
            Expired
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">
            Unknown
          </Badge>
        )
    }
  }

  return (
    <header className="bg-agency-darker/30 backdrop-blur-sm border-b border-agency-green/10 px-4 md:px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side */}
        <div className="flex items-center gap-4">
          {showMenuButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="text-agency-white-muted hover:text-white hover:bg-agency-green/10"
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}

          <div>
            <h1 className="text-xl font-semibold text-white">
              Welcome back, {user?.full_name?.split(' ')[0] || 'User'}!
            </h1>
            <p className="text-sm text-agency-white-muted">
              Manage your projects and track progress
            </p>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-4">
          {/* Payment Status */}
          {user && (
            <div className="hidden md:flex items-center gap-2">
              {getPaymentStatusBadge(user.payment_status)}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleRefreshPayment}
                disabled={isRefreshing}
                className="text-agency-white-muted hover:text-white hover:bg-agency-green/10"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                />
              </Button>
            </div>
          )}

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative text-agency-white-muted hover:text-white hover:bg-agency-green/10"
              >
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 bg-agency-green text-agency-dark text-xs">
                  3
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-80 bg-agency-darker border-agency-green/20"
            >
              <DropdownMenuLabel className="text-white">
                Notifications
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-agency-green/20" />
              <DropdownMenuItem className="text-agency-white-muted hover:text-white hover:bg-agency-green/10">
                <div className="flex flex-col gap-1">
                  <p className="font-medium">Project Update</p>
                  <p className="text-xs">
                    Your website design is ready for review
                  </p>
                  <p className="text-xs text-agency-white-muted">2 hours ago</p>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-agency-white-muted hover:text-white hover:bg-agency-green/10">
                <div className="flex flex-col gap-1">
                  <p className="font-medium">New Message</p>
                  <p className="text-xs">
                    You have a new message from the team
                  </p>
                  <p className="text-xs text-agency-white-muted">1 day ago</p>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-agency-white-muted hover:text-white hover:bg-agency-green/10">
                <div className="flex flex-col gap-1">
                  <p className="font-medium">Payment Confirmed</p>
                  <p className="text-xs">
                    Your payment has been processed successfully
                  </p>
                  <p className="text-xs text-agency-white-muted">3 days ago</p>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-10 w-10 rounded-full"
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage
                    src={
                      user?.email
                        ? `https://api.dicebear.com/7.x/initials/svg?seed=${user.email}`
                        : ''
                    }
                  />
                  <AvatarFallback className="bg-agency-green text-agency-dark">
                    {user?.full_name ? getInitials(user.full_name) : 'U'}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-56 bg-agency-darker border-agency-green/20"
            >
              <DropdownMenuLabel className="text-white">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {user?.full_name || 'User'}
                  </p>
                  <p className="text-xs text-agency-white-muted">
                    {user?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-agency-green/20" />
              <DropdownMenuItem className="text-agency-white-muted hover:text-white hover:bg-agency-green/10">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-agency-white-muted hover:text-white hover:bg-agency-green/10">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-agency-green/20" />
              <DropdownMenuItem
                onClick={handleSignOut}
                className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

export default DashboardHeader
