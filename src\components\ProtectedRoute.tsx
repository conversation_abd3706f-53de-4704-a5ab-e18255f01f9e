import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2, Lock, CreditCard } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface ProtectedRouteProps {
  children: React.ReactNode
  requirePayment?: boolean
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requirePayment = true
}) => {
  const { user, loading, isPaymentVerified } = useAuth()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-agency-dark flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Check payment verification if required
  if (requirePayment && !isPaymentVerified) {
    return (
      <div className="min-h-screen bg-agency-dark flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-agency-darker/50 border-agency-green/20">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-agency-green/10 rounded-full w-fit">
              <CreditCard className="h-8 w-8 text-agency-green" />
            </div>
            <CardTitle className="text-white text-xl">
              Payment Required
            </CardTitle>
            <CardDescription className="text-agency-white-muted">
              You need to complete your payment to access the dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-agency-dark/50 p-4 rounded-lg border border-agency-green/10">
              <h4 className="text-white font-medium mb-2">
                What you get access to:
              </h4>
              <ul className="text-agency-white-muted text-sm space-y-1">
                <li>• Project progress tracking</li>
                <li>• Direct communication with our team</li>
                <li>• Document and file management</li>
                <li>• Payment history and invoices</li>
                <li>• Real-time project updates</li>
              </ul>
            </div>

            <div className="flex flex-col gap-3">
              <Button
                className="w-full bg-agency-green hover:bg-agency-green/90 text-agency-dark font-medium"
                onClick={() => (window.location.href = '/pricing')}
              >
                View Pricing Plans
              </Button>

              <Button
                variant="outline"
                className="w-full border-agency-green/20 text-agency-white-muted hover:bg-agency-green/10"
                onClick={() => (window.location.href = '/contact')}
              >
                Contact Support
              </Button>
            </div>

            <div className="text-center pt-4 border-t border-agency-green/10">
              <p className="text-xs text-agency-white-muted">
                Already paid? Contact us at{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-agency-green hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Render protected content
  return <>{children}</>
}

// Higher-order component for easier usage
export const withProtection = <P extends object>(
  Component: React.ComponentType<P>,
  requirePayment = true
) => {
  return (props: P) => (
    <ProtectedRoute requirePayment={requirePayment}>
      <Component {...props} />
    </ProtectedRoute>
  )
}

export default ProtectedRoute
