# Registration Modal Form Documentation

## Overview

The `RegistrationModal` component has been updated to replace the Google Forms iframe with a custom React form component. This provides better user experience, data control, and integration capabilities.

## Features

### Form Sections

1. **Essential Information**
   - Full Name (required)
   - Email Address (required with validation)
   - Phone Number (optional)

2. **Engagement Qualification**
   - Motivation dropdown with predefined options

3. **Payment Intent**
   - Radio buttons for payment preference
   - "Pay Now" or "Reserve Now, Pay Later (48h Hold)"

4. **Submission**
   - Submit button with loading states
   - Trust indicators and security messaging

### Technical Implementation

- **Form Management**: React Hook Form with Zod validation
- **UI Components**: shadcn/ui components for consistency
- **State Management**: Proper loading, success, and error states
- **Animations**: Maintains existing GSAP modal animations
- **TypeScript**: Full type safety with SOLID principles

## Usage

The modal maintains the same API as before:

```tsx
<RegistrationModal
  isOpen={isModalOpen}
  onClose={closeModal}
  eventTitle="AI & Code Workshop"
  registrationUrl="https://forms.gle/example" // Now optional, kept for backward compatibility
/>
```

## Form Validation

The form includes comprehensive validation:

- **Full Name**: Minimum 2 characters
- **Email**: Valid email format required
- **Phone**: Optional field
- **Motivation**: Required selection from dropdown
- **Payment Intent**: Required radio button selection

## Form Submission Flow

1. **Validation**: Client-side validation with Zod schema
2. **Submission**: Calls `submitRegistration` action handler
3. **Success Handling**: 
   - Shows success message
   - Handles payment redirects for "Pay Now" option
   - Displays registration ID
4. **Error Handling**: Shows error state with retry option

## Backend Integration

The form uses action handlers in `src/lib/registrationActions.ts`:

- `submitRegistration()`: Main submission handler
- `handlePostRegistration()`: Post-submission actions (analytics, notifications)
- `validateRegistrationData()`: Additional business logic validation

## Customization

### Adding New Fields

1. Update the Zod schema in `RegistrationModal.tsx`
2. Add the field to the form UI
3. Update the `RegistrationFormData` type in `src/types/registration.ts`

### Modifying Validation

Update the `registrationSchema` object with new validation rules:

```tsx
const registrationSchema = z.object({
  // Add new fields or modify existing validation
  fullName: z.string().min(2, 'Custom error message'),
  // ...
})
```

### Custom Styling

The form uses Tailwind CSS classes and can be customized by modifying the className props on form elements.

## API Integration

To integrate with a real backend API:

1. Update `submitRegistration()` in `src/lib/registrationActions.ts`
2. Replace the mock implementation with actual API calls
3. Handle authentication if required
4. Update error handling for specific API responses

## Testing

The form can be tested by:

1. Opening the registration modal
2. Filling out the form with various data combinations
3. Testing validation by submitting incomplete forms
4. Verifying success and error states

## Migration Notes

- The Google Forms iframe has been completely replaced
- All existing modal animations and styling are preserved
- The component API remains backward compatible
- Registration data is now captured locally before submission

## Future Enhancements

Potential improvements:

- Add form field animations
- Implement progressive form sections
- Add file upload capabilities
- Integrate with CRM systems
- Add social media login options
- Implement form analytics tracking
